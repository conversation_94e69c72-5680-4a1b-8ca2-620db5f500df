// pages/noise-test/noise-test.js
const wordMapping = require('../../config/word-mapping.js');
const resourceManager = require('../../utils/resourceManager');

Page({
  data: {
    // 测试状态
    testStatus: 'ready', // ready, testing, completed

    // 噪音级别配置
    currentLevel: 0,
    noiseLevels: [
      { level: 30, name: '30dB噪音', description: '轻度噪音环境' },
      { level: 20, name: '20dB噪音', description: '中度噪音环境' },
      { level: 10, name: '10dB噪音', description: '重度噪音环境' }
    ],

    // 当前测试词语
    currentWords: [],
    currentWordText: '',
    currentWordGender: 'male',
    currentWord: 0,

    // 图片选择相关
    imageOptions: [],
    selectedImageIndex: -1,
    correctImageIndex: -1,
    isConfirming: false,

    // 测试结果
    testResults: [],
    currentLevelResults: [],

    // UI状态
    isPlaying: false,
    userResponse: '',

    // 进度相关
    totalSteps: 6, // 3组 × 2个词汇
    completedSteps: 0,

    // 音频相关
    audioContext: null,
    audioFormat: 'mp3'
  },

  onLoad: function(options) {
    console.log('🔊 噪音测试页面加载');
    this.initAudio();

    // 直接开始测试，跳过准备阶段
    setTimeout(() => {
      this.startNoiseTest();
    }, 500); // 延迟500ms确保音频初始化完成
  },

  // 初始化音频
  initAudio: function() {
    try {
      this.audioContext = wx.createInnerAudioContext();
      this.audioContext.onError((error) => {
        console.error('音频播放错误:', error);
        wx.showToast({
          title: '音频播放失败',
          icon: 'none'
        });
      });

      this.audioContext.onEnded(() => {
        this.setData({ isPlaying: false });
      });

      console.log('✅ 音频上下文初始化成功');
    } catch (error) {
      console.error('❌ 音频初始化失败:', error);
    }
  },

  // 开始噪音测试
  startNoiseTest: function() {
    console.log('🎯 开始噪音测试');

    this.setData({
      testStatus: 'testing',
      currentLevel: 0,
      currentWord: 0,
      testResults: [],
      currentLevelResults: [],
      completedSteps: 0,
      selectedImageIndex: -1,
      isConfirming: false
    });

    this.startLevelTest();
  },

  // 开始某个噪音级别的测试
  startLevelTest: function() {
    const { currentLevel, noiseLevels } = this.data;

    console.log(`🔊 开始 ${noiseLevels[currentLevel].name} 测试`);

    // 从噪音测试词汇中随机选择2个词汇
    const allWordsData = wordMapping.getAllWords();
    const allWords = allWordsData.map(item => item.word); // 提取词汇字符串
    const shuffledWords = [...allWords].sort(() => Math.random() - 0.5);
    const selectedWords = shuffledWords.slice(0, 2);

    console.log(`📚 可用词汇: ${allWords.length}个`);
    console.log(`🎲 随机选择: ${selectedWords.join(', ')}`);

    // 为选中的词汇分配性别（第一个男声，第二个女声）
    const wordsWithGender = selectedWords.map((word, index) => {
      const gender = index === 0 ? 'male' : 'female';
      const audioPath = this.getAudioPath(word, gender);
      const imagePath = this.getImagePath(word);

      return {
        word: word,
        gender: gender,
        audioPath: audioPath,
        imagePath: imagePath,
        resourceReady: audioPath !== null && imagePath !== null
      };
    });

    console.log(`🎲 选中的测试词汇:`, wordsWithGender);

    // 检查资源是否准备好
    const allResourcesReady = wordsWithGender.every(item => item.resourceReady);
    if (!allResourcesReady) {
      wx.showModal({
        title: '资源准备中',
        content: '测试资源正在后台下载，请稍候再试',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    this.setData({
      currentWords: wordsWithGender,
      currentWord: 0,
      currentLevelResults: []
    });

    this.playCurrentWord();
  },

  // 播放当前词语
  playCurrentWord: function() {
    const { currentWords, currentWord, noiseLevels, currentLevel } = this.data;

    if (currentWord >= currentWords.length) {
      this.completeLevelTest();
      return;
    }

    const wordData = currentWords[currentWord];
    const wordText = wordData.word;
    const wordGender = wordData.gender;
    const noiseLevel = noiseLevels[currentLevel];

    console.log(`🎵 播放音频 (${wordGender === 'female' ? '女声' : '男声'}) - ${noiseLevel.name}`);

    // 先设置播放状态，不显示图片选项
    this.setData({
      currentWordText: wordText,
      currentWordGender: wordGender,
      isPlaying: true,
      userResponse: '',
      imageOptions: [], // 播放时不显示图片
      correctImageIndex: -1,
      selectedImageIndex: -1,
      isConfirming: false
    });

    // 播放音频（使用原始音量，不降低）
    this.playAudio(wordData.audioPath, 1.0); // 音量系数为1.0，保持原始音量
  },

  // 获取音频路径（优先使用本地缓存）
  getAudioPath: function(word, gender) {
    try {
      // 尝试获取本地缓存路径
      const localPath = resourceManager.getLocalResourcePath(word, 'audio', gender);

      // 检查本地文件是否存在
      try {
        wx.getFileSystemManager().statSync(localPath);
        console.log('使用本地音频:', localPath);
        return localPath;
      } catch (error) {
        // 本地缓存不存在，返回null表示资源未准备好
        console.log('音频资源未准备好:', word, gender);
        return null;
      }
    } catch (error) {
      console.error('获取音频路径失败:', error);
      return null;
    }
  },

  // 获取图片路径（优先使用本地缓存）
  getImagePath: function(word) {
    try {
      // 尝试获取本地缓存路径
      const localPath = resourceManager.getLocalResourcePath(word, 'image');

      // 检查本地文件是否存在
      try {
        wx.getFileSystemManager().statSync(localPath);
        console.log('使用本地图片:', localPath);
        return localPath;
      } catch (error) {
        // 本地缓存不存在，返回null表示资源未准备好
        console.log('图片资源未准备好:', word);
        return null;
      }
    } catch (error) {
      console.error('获取图片路径失败:', error);
      return null;
    }
  },

  // 播放音频
  playAudio: function(audioPath, volume = 1.0) {
    if (!this.audioContext) {
      console.error('音频上下文未初始化');
      return;
    }

    try {
      // 设置音频播放完成回调
      this.audioContext.onEnded(() => {
        console.log('🎵 音频播放完成，显示图片选项');
        this.showImageOptions();
      });

      this.audioContext.src = audioPath;
      this.audioContext.volume = volume;
      this.audioContext.play();

      console.log(`🔊 播放音频: ${audioPath}, 音量: ${volume}`);
    } catch (error) {
      console.error('音频播放失败:', error);
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
      // 播放失败时也显示图片选项
      this.showImageOptions();
    }
  },

  // 显示图片选项
  showImageOptions: function() {
    const { currentWordText } = this.data;

    if (!currentWordText) return;

    const imageOptionsData = wordMapping.generateImageOptions(currentWordText);
    this.setData({
      isPlaying: false,
      imageOptions: imageOptionsData.options,
      correctImageIndex: imageOptionsData.correctIndex
    });
  },

  // 选择图片
  selectImage: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ selectedImageIndex: index });
  },

  // 确认选择
  confirmSelection: function() {
    const { selectedImageIndex, correctImageIndex, imageOptions, isConfirming } = this.data;

    // 防重复点击
    if (isConfirming) {
      return;
    }

    if (selectedImageIndex === -1) {
      wx.showToast({
        title: '请先选择一个图片',
        icon: 'none'
      });
      return;
    }

    // 设置按钮为处理中状态
    this.setData({ isConfirming: true });

    const selectedWord = imageOptions[selectedImageIndex].word;
    const isCorrect = selectedImageIndex === correctImageIndex;

    this.setData({ userResponse: selectedWord });
    this.recordResponse(selectedWord, isCorrect);
  },

  // 记录响应结果
  recordResponse: function(response, isCorrect) {
    console.log('🎯 记录用户回答:', {
      word: this.data.currentWordText,
      gender: this.data.currentWordGender,
      response: response,
      isCorrect: isCorrect,
      currentLevel: this.data.currentLevel,
      currentWord: this.data.currentWord
    });

    const { currentLevelResults, currentWordText, currentWordGender, noiseLevels, currentLevel } = this.data;

    const result = {
      word: currentWordText,
      gender: currentWordGender,
      response: response,
      isCorrect: isCorrect,
      noiseLevel: noiseLevels[currentLevel].level,
      timestamp: new Date().getTime()
    };

    currentLevelResults.push(result);

    // 用户提交答案后，进度+1
    const newCompletedSteps = this.data.completedSteps + 1;

    this.setData({
      currentLevelResults,
      currentWord: this.data.currentWord + 1,
      completedSteps: newCompletedSteps,
      isConfirming: false,
      selectedImageIndex: -1
    });

    console.log('⏭️ 下一个词语索引:', this.data.currentWord);
    console.log('📚 当前级别总词语数:', this.data.currentWords.length);
    console.log('📈 完成步骤数:', newCompletedSteps, '/', this.data.totalSteps);

    // 短暂显示结果后继续下一个词语
    setTimeout(() => {
      this.playCurrentWord();
    }, 1000);
  },

  // 完成当前级别测试
  completeLevelTest: function() {
    const { currentLevelResults, testResults, currentLevel, noiseLevels } = this.data;

    console.log(`✅ 完成 ${noiseLevels[currentLevel].name} 级别测试`);

    // 计算当前级别的正确率
    const correctCount = currentLevelResults.filter(r => r.isCorrect).length;
    const accuracy = (correctCount / currentLevelResults.length * 100).toFixed(1);

    const levelResult = {
      level: noiseLevels[currentLevel].level,
      name: noiseLevels[currentLevel].name,
      results: currentLevelResults,
      correctCount: correctCount,
      totalCount: currentLevelResults.length,
      accuracy: accuracy
    };

    testResults.push(levelResult);

    this.setData({
      testResults,
      currentLevel: this.data.currentLevel + 1
    });

    // 检查是否完成所有级别
    if (this.data.currentLevel >= noiseLevels.length) {
      this.completeAllTests();
    } else {
      // 继续下一个级别
      setTimeout(() => {
        this.startLevelTest();
      }, 1500);
    }
  },

  // 完成所有测试
  completeAllTests: function() {
    console.log('🎉 完成所有噪音测试');

    // 计算总体结果
    const allResults = this.data.testResults.flatMap(level => level.results);
    const totalCorrect = allResults.filter(r => r.isCorrect).length;
    const totalTests = allResults.length;
    const overallAccuracy = (totalCorrect / totalTests * 100).toFixed(1);

    console.log(`📊 总体结果: ${totalCorrect}/${totalTests} (${overallAccuracy}%)`);

    this.setData({
      testStatus: 'completed',
      overallAccuracy: overallAccuracy,
      totalCorrect: totalCorrect,
      totalTests: totalTests
    });

    // 保存测试结果到数据库
    this.saveTestResults();
  },

  // 保存测试结果
  saveTestResults: function() {
    const { testResults, overallAccuracy, totalCorrect, totalTests } = this.data;

    const testData = {
      type: 'noise', // 噪音测试类型
      testDate: new Date().toLocaleString(),
      testResults: testResults,
      overallAccuracy: parseFloat(overallAccuracy),
      totalCorrect: totalCorrect,
      totalTests: totalTests,
      timestamp: new Date().getTime()
    };

    console.log('💾 保存噪音测试结果:', testData);

    // 计算噪音测试分数（与听音辨图保持一致的分数形式）
    const accuracyNum = parseFloat(overallAccuracy);
    const noiseScore = Math.round(accuracyNum); // 转换为分数

    console.log('💾 准备保存噪音测试数据:', {
      overallAccuracy: accuracyNum,
      noiseScore: noiseScore,
      totalCorrect: totalCorrect,
      totalTests: totalTests
    });

    // 调用云函数保存数据
    wx.cloud.callFunction({
      name: 'saveTestResult',
      data: {
        testResults: testResults,
        overallAccuracy: accuracyNum,
        noiseScore: noiseScore, // 添加分数字段
        totalCorrect: totalCorrect,
        totalTests: totalTests,
        testDate: new Date(),
        testType: 'noise_test'
      }
    }).then(res => {
      console.log('噪音测试结果保存成功', res);
      wx.showToast({
        title: '测试结果已保存',
        icon: 'success'
      });
    }).catch(err => {
      console.error('噪音测试结果保存失败', err);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },

  // 查看详细结果
  viewDetailedResults: function() {
    const { testResults, overallAccuracy, totalCorrect, totalTests } = this.data;

    // 使用encodeURIComponent编码JSON数据，与历史记录跳转保持一致
    const encodedResults = encodeURIComponent(JSON.stringify(testResults));

    wx.navigateTo({
      url: `/pages/test-result/test-result?type=noise&accuracy=${overallAccuracy}&correct=${totalCorrect}&total=${totalTests}&results=${encodedResults}`
    });
  },

  // 重新测试
  retakeTest: function() {
    this.setData({
      testStatus: 'ready',
      currentLevel: 0,
      currentWord: 0,
      testResults: [],
      currentLevelResults: [],
      completedSteps: 0,
      selectedImageIndex: -1,
      isConfirming: false
    });
  },

  // 页面卸载时清理音频
  onUnload: function() {
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  }
});