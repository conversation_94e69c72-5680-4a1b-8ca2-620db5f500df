<!--pages/noise-test/noise-test.wxml-->
<view class="container">

  <!-- 测试进行中 -->
  <view wx:if="{{testStatus === 'testing'}}" class="testing-section">

    <!-- 测试进度 -->
    <view class="progress-section">
      <view class="progress-header">
        <text class="current-level">正在测试 {{noiseLevels[currentLevel].name}}</text>
        <text class="progress-text">总进度 {{completedSteps}}/{{totalSteps}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(completedSteps / totalSteps) * 100}}%"></view>
      </view>
    </view>

    <!-- 当前词语显示 -->
    <view class="word-display">
      <view wx:if="{{isPlaying}}" class="playing-state">
        <view class="audio-wave">🔊</view>
        <text class="playing-text">正在播放音频...</text>
        <text class="playing-hint">请仔细聆听</text>
      </view>

      <view wx:else class="response-state">
        <text class="response-prompt">请选择对应的图片</text>
      </view>
    </view>

    <!-- 图片选择响应区域 -->
    <view wx:if="{{!isPlaying && testStatus === 'testing'}}" class="image-response">
      <view class="image-options">
        <view
          wx:for="{{imageOptions}}"
          wx:key="index"
          class="image-option {{selectedImageIndex === index ? 'selected' : ''}}"
          bindtap="selectImage"
          data-index="{{index}}"
        >
          <image
            src="{{item.imagePath}}"
            class="option-image"
            mode="cover"
          />
          <view wx:if="{{selectedImageIndex === index}}" class="selected-mark">
            <text class="checkmark">✓</text>
          </view>
        </view>
      </view>

      <view wx:if="{{selectedImageIndex !== -1}}" class="confirm-section">
        <button
          class="confirm-btn {{isConfirming ? 'disabled' : ''}}"
          bindtap="confirmSelection"
          disabled="{{isConfirming}}"
        >
          {{isConfirming ? '处理中...' : '确认选择'}}
        </button>
      </view>
    </view>

  </view>

  <!-- 测试完成 -->
  <view wx:if="{{testStatus === 'completed'}}" class="completed-section">

    <!-- 噪音测试结果展示 -->
    <view class="noise-result">
      <view class="result-header">
        <text class="result-title">🎉 测试完成</text>
        <text class="result-subtitle">您的噪音测试结果</text>
      </view>

      <view class="overall-score">
        <text class="score-number">{{overallAccuracy}}%</text>
        <text class="score-unit">准确率</text>
      </view>

      <view class="score-details">
        <text class="score-text">正确: {{totalCorrect}} / {{totalTests}}</text>
      </view>
    </view>

    <!-- 详细结果 -->
    <view class="detailed-results">
      <text class="details-title">各级别结果</text>
      <view wx:for="{{testResults}}" wx:key="index" class="level-result">
        <view class="level-header">
          <text class="level-name">{{item.name}}</text>
          <text class="level-accuracy">{{item.accuracy}}%</text>
        </view>
        <text class="level-detail">{{item.correctCount}}/{{item.totalCount}} 正确</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="detail-btn" bindtap="viewDetailedResults">
        查看详细报告
      </button>
      <button class="retry-btn" bindtap="retakeTest">
        重新测试
      </button>
    </view>
  </view>

</view>
