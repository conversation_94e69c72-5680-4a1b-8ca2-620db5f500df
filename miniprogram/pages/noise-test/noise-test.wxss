/* pages/noise-test/noise-test.wxss */

/* 完全复用听音辩图页面的样式 */
@import "../hearing-test/hearing-test.wxss";
@import "../../styles/theme.wxss";

/* 噪音测试特有的样式覆盖 - 与听音辩图保持一致 */
.container {
  background: var(--bg-primary);
}

/* 噪音级别显示样式调整 */
.current-level {
  color: var(--text-primary);
  font-size: 32rpx;
  font-weight: 600;
}

.progress-text {
  color: var(--text-secondary);
  font-size: 24rpx;
}

/* 播放提示样式 */
.playing-hint {
  font-size: 28rpx;
  color: #999;
  font-weight: normal;
  margin-top: 10rpx;
}

/* 噪音测试结果样式 */
.noise-result {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.overall-score {
  margin: 40rpx 0;
}

.score-number {
  font-size: 72rpx;
  font-weight: bold;
  color: #ff6b6b;
  display: block;
}

.score-unit {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-top: 10rpx;
}

.score-details {
  margin-top: 30rpx;
}

.score-text {
  font-size: 28rpx;
  color: #333;
}

/* 详细结果样式 */
.detailed-results {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.level-result {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.level-result:last-child {
  border-bottom: none;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.level-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.level-accuracy {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.level-detail {
  font-size: 24rpx;
  color: #666;
}

/* 结果页面按钮样式 */
.detail-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
}

.retry-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 40rpx;
}
