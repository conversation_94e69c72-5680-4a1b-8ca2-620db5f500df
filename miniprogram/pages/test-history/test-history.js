// pages/test-history/test-history.js
Page({
  data: {
    historyList: [],
    filteredList: [],
    loading: true,
    totalCount: 0,
    filterType: 'all', // 过滤类型：all, hearing_sai, noise_test
    filterOptions: [
      { value: 'all', label: '全部测试' },
      { value: 'hearing_sai', label: '听音辨图' },
      { value: 'noise_test', label: '噪音测试' }
    ]
  },

  onLoad: function(options) {
    this.loadTestHistory();
  },

  // 加载测试历史
  loadTestHistory: function() {
    wx.showLoading({ title: '加载中...' });
    
    wx.cloud.callFunction({
      name: 'getTestHistory',
      data: {}
    }).then(res => {
      wx.hideLoading();
      console.log('获取历史记录成功', res);
      
      if (res.result.success) {
        // 处理历史数据，格式化识别率
        const processedHistory = (res.result.data || []).map(item => {
          // 检查testResults是否存在
          if (!item.testResults || !Array.isArray(item.testResults)) {
            return {
              ...item,
              testResults: []
            };
          }

          // 处理测试结果中的识别率
          const processedTestResults = item.testResults.map(result => {
            if (item.testType === 'noise_test') {
              // 噪音测试使用accuracy字段
              const accuracy = typeof result.accuracy === 'number' ? result.accuracy : parseFloat(result.accuracy) || 0;
              return {
                ...result,
                accuracy: accuracy.toFixed(1)
              };
            } else {
              // 听音辨图测试使用recognitionRate字段
              const rate = typeof result.recognitionRate === 'number' ? result.recognitionRate : 0;
              return {
                ...result,
                recognitionRateFormatted: rate.toFixed(1)
              };
            }
          });

          // 确保噪音测试的数据格式正确
          let processedItem = {
            ...item,
            testResults: processedTestResults
          };

          // 如果是噪音测试，处理数据并转换为分数形式
          if (item.testType === 'noise_test') {
            const accuracy = item.overallAccuracy !== undefined
              ? (typeof item.overallAccuracy === 'number' ? item.overallAccuracy : parseFloat(item.overallAccuracy) || 0)
              : 0;

            // 优先使用数据库中的noiseScore，如果没有则从overallAccuracy计算
            let noiseScore = 0;
            if (item.noiseScore !== undefined) {
              // 使用数据库中已保存的分数
              noiseScore = typeof item.noiseScore === 'number' ? item.noiseScore : parseInt(item.noiseScore) || 0;
            } else {
              // 从准确率计算分数（兼容旧数据）
              noiseScore = Math.round(accuracy);
            }

            processedItem.overallAccuracy = accuracy;
            processedItem.noiseScore = noiseScore;

            console.log('处理噪音测试数据:', {
              原始准确率: item.overallAccuracy,
              数据库分数: item.noiseScore,
              处理后准确率: processedItem.overallAccuracy,
              最终分数: processedItem.noiseScore,
              分数来源: item.noiseScore !== undefined ? '数据库' : '计算得出'
            });
          }

          return processedItem;
        });

        this.setData({
          historyList: processedHistory,
          totalCount: res.result.totalCount || processedHistory.length,
          loading: false
        });

        // 应用当前过滤器
        this.applyFilter();
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取历史记录失败', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 查看详细结果
  viewDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const testData = this.data.filteredList[index]; // 使用filteredList而不是historyList

    console.log('查看详情，测试数据:', testData);
    console.log('测试类型:', testData.testType);

    // 根据测试类型进行不同的处理
    if (testData.testType === 'noise_test') {
      this.viewNoiseTestDetail(testData);
    } else {
      this.viewHearingTestDetail(testData);
    }
  },

  // 查看听音辨图测试详情
  viewHearingTestDetail: function(testData) {
    try {
      // 传递听音辨图测试的数据结构
      const originalTestResults = testData.testResults.map(result => ({
        level: result.level,
        recognitionRate: result.recognitionRate,
        correctCount: result.correctCount,
        totalCount: result.totalCount,
        results: result.results || []
      }));

      wx.setStorageSync('testResults', originalTestResults);
      console.log('听音辨图历史数据已保存到本地存储');

      wx.navigateTo({
        url: '/pages/test-result/test-result'
      });
    } catch (e) {
      console.error('保存听音辨图历史数据失败:', e);
      wx.showToast({
        title: '数据传递失败',
        icon: 'none'
      });
    }
  },

  // 查看噪音测试详情
  viewNoiseTestDetail: function(testData) {
    try {
      // 手动构造URL参数，兼容小程序环境
      const accuracy = testData.overallAccuracy || 0;
      const correct = testData.totalCorrect || 0;
      const total = testData.totalTests || 0;
      const results = encodeURIComponent(JSON.stringify(testData.testResults || []));

      const url = `/pages/test-result/test-result?type=noise&accuracy=${accuracy}&correct=${correct}&total=${total}&results=${results}`;

      console.log('噪音测试跳转参数:', {
        type: 'noise',
        accuracy: accuracy,
        correct: correct,
        total: total,
        resultsLength: testData.testResults ? testData.testResults.length : 0
      });

      wx.navigateTo({
        url: url
      });
    } catch (e) {
      console.error('跳转噪音测试详情失败:', e);
      wx.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  // 删除记录
  deleteRecord: function(e) {
    const index = e.currentTarget.dataset.index;
    const record = this.data.historyList[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条测试记录吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以调用云函数删除记录
          const newList = [...this.data.historyList];
          newList.splice(index, 1);
          this.setData({ historyList: newList });
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 过滤类型改变
  onFilterChange: function(e) {
    const filterType = e.detail.value;
    this.setData({ filterType });
    this.applyFilter();
  },

  // 应用过滤器
  applyFilter: function() {
    const { historyList, filterType } = this.data;

    let filteredList = historyList;

    if (filterType !== 'all') {
      filteredList = historyList.filter(item => item.testType === filterType);
    }

    this.setData({
      filteredList,
      totalCount: filteredList.length
    });
  },

  // 开始新测试
  startNewTest: function() {
    wx.navigateTo({
      url: '/pages/hearing-test/hearing-test'
    });
  }
});
