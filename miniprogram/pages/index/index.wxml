<!--index.wxml - 极简主义重设计-->
<view class="page-container">
  
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="app-header">
      <text class="app-title">AI听健</text>
      <text class="app-subtitle">专业听力健康管理</text>
    </view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-content page-content">
    
    <!-- 四宫格功能模块 -->
    <view class="function-grid">
      <!-- 听音辩图 -->
      <view class="function-card primary-card" bindtap="startHearingTest">
        <view class="card-icon-wrapper">
          <text class="card-icon">🎧</text>
        </view>
        <text class="card-title">听音辩图</text>
        <text class="card-desc">专业听力评估测试</text>
      </view>

      <!-- 噪音测试 -->
      <view class="function-card secondary-card" bindtap="startNoiseTest">
        <view class="card-icon-wrapper">
          <text class="card-icon">🔉</text>
        </view>
        <text class="card-title">噪音测试</text>
        <text class="card-desc">噪音环境听力测试</text>
      </view>

      <!-- 测试记录 -->
      <view class="function-card tertiary-card" bindtap="viewHistory">
        <view class="card-icon-wrapper">
          <text class="card-icon">📋</text>
        </view>
        <text class="card-title">测试记录</text>
        <text class="card-desc">查看历史测试数据</text>
      </view>

      <!-- AI助手 -->
      <view class="function-card quaternary-card" bindtap="openAIChat">
        <view class="card-icon-wrapper">
          <text class="card-icon">✨</text>
        </view>
        <text class="card-title">AI助手</text>
        <text class="card-desc">智能听力健康咨询</text>
      </view>
    </view>

    <!-- 测试特点信息卡片 -->
    <view class="info-card">
      <view class="info-header">
        <text class="info-title">测试特点</text>
      </view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-icon">🎯</text>
          <text class="info-text">三级声强测试</text>
        </view>
        <view class="info-item">
          <text class="info-icon">🎧</text>
          <text class="info-text">专业听力评估</text>
        </view>
        <view class="info-item">
          <text class="info-icon">📋</text>
          <text class="info-text">详细测试报告</text>
        </view>
        <view class="info-item">
          <text class="info-icon">🔍</text>
          <text class="info-text">听音识别图片</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部功能导航 - 简化版 -->
  <view class="bottom-nav">
    <view class="nav-item {{currentTab === 'home' ? 'active' : ''}}" bindtap="switchTab" data-tab="home">
      <text class="nav-icon">🏠</text>
      <text class="nav-label">首页</text>
    </view>
    <view class="nav-item {{currentTab === 'ai' ? 'active' : ''}}" bindtap="switchTab" data-tab="ai">
      <text class="nav-icon">✨</text>
      <text class="nav-label">AI助手</text>
    </view>
  </view>

</view>
