/* 极简主义首页样式 */
@import "../../styles/theme.wxss";

.page-container {
  min-height: 100vh;
  background: #f8fafc;
  color: #374151;
}

/* 顶部标题区域 */
.header-section {
  padding: 20rpx 48rpx 10rpx;
  text-align: center;
  background: #ffffff;
}

.app-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #3b82f6;
  letter-spacing: -1px;
}

.app-subtitle {
  font-size: 24rpx;
  color: #475569;
  font-weight: 400;
}

/* 主要内容区域 */
.main-content {
  padding: 8rpx 32rpx;
  padding-bottom: 200rpx; /* 为底部导航预留空间 */
}

/* 四宫格功能模块 */
.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.function-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
}

.function-card:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4px 16px 0 rgba(59, 130, 246, 0.12);
}

.primary-card {
  background: #f0f9ff;
}

.secondary-card {
  background: #f0fdf4;
}

.tertiary-card {
  background: #faf5ff;
}

.quaternary-card {
  background: #fff7ed;
}

.card-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}

.card-icon {
  font-size: 80rpx;
  color: #475569;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
  display: block;
}

.card-desc {
  font-size: 26rpx;
  color: #475569;
  font-weight: 400;
  line-height: 1.4;
}

.card-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: var(--primary-teal);
  color: #ffffff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: var(--radius-sm);
  font-weight: 500;
}



/* 信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  border: 1px solid #f1f5f9;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.info-header {
  margin-bottom: 32rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #3b82f6 !important;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 确保测试特点标题使用正确颜色 - 最高优先级 */
.info-card .info-header .info-title {
  color: #3b82f6 !important;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* 兜底方案：如果渐变不支持，使用纯色 */
.info-title,
.info-card .info-title,
.info-card .info-header .info-title {
  color: #3b82f6 !important;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.info-icon {
  font-size: 32rpx;
  color: #3b82f6;
}

.info-text {
  font-size: 26rpx;
  color: #475569;
  font-weight: 400;
}

/* 底部导航栏 - 简洁浅色系 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  padding: 24rpx 0 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  gap: 120rpx;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.04);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  transition: all 0.2s ease;
  min-width: 160rpx;
}

.nav-item.active {
  background: #dbeafe;
}

.nav-icon {
  font-size: 56rpx;
  margin-bottom: 12rpx;
  color: #94a3b8;
  transition: all 0.2s ease;
}

.nav-item.active .nav-icon {
  color: #3b82f6;
}

.nav-label {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item.active .nav-label {
  color: #3b82f6;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-section {
    padding: 30rpx 32rpx 15rpx;
  }

  .app-title {
    font-size: 48rpx;
  }

  .main-content {
    padding: 4rpx 24rpx;
    padding-bottom: 180rpx; /* 响应式设计中的底部间距 */
  }

  .function-grid {
    gap: 16rpx;
  }

  .function-card {
    padding: 40rpx 24rpx;
  }

  .card-icon {
    font-size: 64rpx;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
}
