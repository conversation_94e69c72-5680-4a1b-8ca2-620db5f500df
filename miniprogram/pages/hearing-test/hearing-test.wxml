<!--pages/hearing-test/hearing-test.wxml-->
<view class="container">
  
  <!-- 准备阶段 -->
  <view wx:if="{{testStatus === 'ready'}}" class="ready-section">
    <view class="header">
      <text class="title">🎧 听音辩图测试</text>
      <text class="subtitle">听音识别图片，专业听力评估</text>
    </view>

    <!-- 环境监测 -->
    <view class="environment-monitor">
      <view class="monitor-header">
        <text class="monitor-title">
          📊 环境监测
          <text style="font-size: 24rpx; color: #666;">
            {{isRealDetection ? '(真实检测)' : '(模拟检测)'}}
          </text>
        </text>
        <view class="noise-indicator {{isEnvironmentOk ? 'safe' : 'warning'}}">
          <text class="noise-level">{{noiseLevel}}dB</text>
          <text class="noise-status">{{isEnvironmentOk ? '✅ 环境良好' : '⚠️ 环境嘈杂'}}</text>
        </view>
      </view>
      <view class="threshold-info">
        <text>建议噪音水平: 小于 {{noiseThreshold}}dB</text>
        <text wx:if="{{!microphoneAuthorized}}" style="display: block; font-size: 24rpx; color: #999; margin-top: 10rpx;">
          💡 点击"开始测试"时将申请麦克风权限以获得真实环境检测
        </text>
        <text wx:if="{{microphoneAuthorized}}" style="display: block; font-size: 24rpx; color: #4caf50; margin-top: 10rpx;">
          ✅ 已获得麦克风权限，正在使用真实环境检测
        </text>
      </view>
      <view style="margin-top: 20rpx; text-align: center;">
        <button wx:if="{{!microphoneAuthorized}}" size="mini" bindtap="requestMicrophonePermission"
                style="background: #e3f2fd; font-size: 24rpx;">
          🎤 申请麦克风权限
        </button>
      </view>
    </view>



    <!-- 测试说明 -->
    <view wx:if="{{showInstructions}}" class="instructions">
      <view class="instruction-title">📋 测试说明</view>
      <view class="instruction-list">
        <view class="instruction-item">
          <text class="step-number">1</text>
          <text class="step-text">请佩戴耳机，确保在安静环境中进行测试</text>
        </view>
        <view class="instruction-item">
          <text class="step-number">2</text>
          <text class="step-text">系统将在55dB、70dB、85dB三个声强级播放词语</text>
        </view>
        <view class="instruction-item">
          <text class="step-number">3</text>
          <text class="step-text">每个级别播放2个双音节词语，请仔细聆听</text>
        </view>
        <view class="instruction-item">
          <text class="step-number">4</text>
          <text class="step-text">听到词语后，可选择语音复述或按键选择</text>
        </view>
      </view>
    </view>

    <!-- 开始按钮 -->
    <view class="start-section">
      <button
        class="start-btn {{isEnvironmentOk ? 'enabled' : 'disabled'}}"
        bindtap="startTest"
        disabled="{{!isEnvironmentOk}}"
      >
        {{isEnvironmentOk ? '🎯 开始测试' : '⏳ 等待环境安静'}}
      </button>
    </view>
  </view>

  <!-- 测试进行中 -->
  <view wx:if="{{testStatus === 'testing'}}" class="testing-section">
    
    <!-- 测试进度 -->
    <view class="progress-section">
      <view class="progress-header">
        <text class="current-level">正在测试 {{soundLevels[currentLevel].level}}dB</text>
        <text class="progress-text">总进度 {{completedSteps}}/{{totalSteps}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(completedSteps / totalSteps) * 100}}%"></view>
      </view>
    </view>

    <!-- 当前词语显示 -->
    <view class="word-display">
      <view wx:if="{{isPlaying}}" class="playing-state">
        <view class="audio-wave">🔊</view>
        <text class="playing-text">正在播放音频...</text>
        <text class="playing-hint">请仔细聆听</text>
      </view>

      <view wx:else class="response-state">
        <text class="response-prompt">请选择对应的图片</text>
      </view>
    </view>

    <!-- 图片选择响应区域 -->
    <view wx:if="{{!isPlaying && testStatus === 'testing'}}" class="image-response">
      <view class="image-options">
        <view
          wx:for="{{imageOptions}}"
          wx:key="index"
          class="image-option {{selectedImageIndex === index ? 'selected' : ''}}"
          bindtap="selectImage"
          data-index="{{index}}"
        >
          <image
            src="{{item.imagePath}}"
            class="option-image"
            mode="aspectFit"
          />
          <view wx:if="{{selectedImageIndex === index}}" class="selected-mark">
            <text class="checkmark">✓</text>
          </view>
        </view>
      </view>

      <view wx:if="{{selectedImageIndex !== -1}}" class="confirm-section">
        <button
          class="confirm-btn {{isConfirming ? 'disabled' : ''}}"
          bindtap="confirmSelection"
          disabled="{{isConfirming}}"
        >
          {{isConfirming ? '处理中...' : '确认选择'}}
        </button>
      </view>
    </view>

  </view>

  <!-- 测试完成 -->
  <view wx:if="{{testStatus === 'completed'}}" class="completed-section">
    
    <!-- 听音辩图结果展示 -->
    <view class="sai-result">
      <view class="result-header">
        <text class="result-title">🎉 测试完成</text>
        <text class="result-subtitle">您的听音辩图测试结果</text>
      </view>
      
      <view class="sai-score">
        <text class="score-number">{{saiScore}}</text>
        <text class="score-unit">分</text>
      </view>
      
      <view class="sai-level {{saiLevelClass}}">
        <text class="level-text">{{saiLevel}}</text>
      </view>
      
      <view class="level-description">
        <text wx:if="{{saiLevel === '优秀'}}">您的听力状况非常好，能够很好地应对日常社交场景</text>
        <text wx:if="{{saiLevel === '良好'}}">您的听力状况良好，在大多数社交场景中表现不错</text>
        <text wx:if="{{saiLevel === '需关注'}}">建议关注听力健康，在嘈杂环境中可能需要额外注意</text>
        <text wx:if="{{saiLevel === '建议专业检测'}}">建议咨询专业听力师进行详细检测</text>
      </view>
    </view>

    <!-- 详细结果 -->
    <view class="detailed-results">
      <view class="results-title">📊 详细结果</view>
      <view class="level-results">
        <view wx:for="{{testResults}}" wx:key="index" class="level-result">
          <view class="level-info">
            <text class="level-name">{{item.level}}dB</text>
            <text class="level-rate">{{item.recognitionRateFormatted}}%</text>
          </view>
          <view class="level-detail">
            <text class="correct-count">正确: {{item.correctCount}}/{{item.totalCount}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="viewDetailResults">
        📋 查看详细报告
      </button>
      <button class="action-btn primary" bindtap="retryTest">
        🔄 重新测试
      </button>
      <button class="action-btn" bindtap="goHome">
        🏠 返回首页
      </button>
    </view>
  </view>

</view>

<!-- 音频播放已改为使用 wx.createInnerAudioContext -->
