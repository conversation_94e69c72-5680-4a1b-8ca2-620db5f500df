// pages/hearing-test/hearing-test.js
const wordMapping = require('../../config/word-mapping.js');
const resourceManager = require('../../utils/resourceManager');

Page({
  data: {
    // 测试状态
    testStatus: 'ready', // ready, testing, completed
    currentLevel: 0, // 当前声强级别 (0: 55dB, 1: 70dB, 2: 85dB)
    currentWord: 0, // 当前词语索引

    // 声强级别配置
    soundLevels: [
      { level: 55, name: '55分贝' },
      { level: 70, name: '70分贝' },
      { level: 85, name: '85分贝' }
    ],

    // 统一词库 - 使用新的测试级别配置
    wordBank: [
      // 55dB 测试词汇（分组便于随机选择）
      [
        wordMapping.TEST_LEVELS[55].slice(0, 8),  // 交通工具类
        wordMapping.TEST_LEVELS[55].slice(8, 16)  // 水果类
      ],
      // 70dB 测试词汇（分组便于随机选择）
      [
        wordMapping.TEST_LEVELS[70].slice(0, 9),  // 电器类
        wordMapping.TEST_LEVELS[70].slice(9, 16)  // 职业类
      ],
      // 85dB 测试词汇（自然类）
      [
        wordMapping.TEST_LEVELS[85]  // 自然类
      ]
    ],

    // 当前测试词语
    currentWords: [],
    currentWordText: '',
    currentWordGender: 'male', // 当前词语的性别

    // 测试进度（从0开始，用户提交答案后+1）
    completedSteps: 0,
    totalSteps: 6, // 3个级别 × 2个词语 = 6步

    // 音频文件状态
    audioFilesReady: true,

    // 环境监测
    noiseLevel: 35, // 初始噪音级别（模拟良好环境）
    isEnvironmentOk: true, // 初始状态设为良好
    noiseThreshold: 40, // 噪音阈值 40dB（真实环境下调整为更合理的值）
    isRealDetection: false, // 是否使用真实检测
    microphoneAuthorized: false, // 麦克风权限状态

    // 用户响应
    userResponse: '',

    // 图片选择相关
    imageOptions: [], // 当前显示的4个图片选项
    selectedImageIndex: -1, // 用户选择的图片索引
    correctImageIndex: -1, // 正确答案的索引
    isConfirming: false, // 防重复点击确认按钮
    
    // 测试结果
    testResults: [],
    currentLevelResults: [],
    
    // UI状态
    isPlaying: false,
    showInstructions: true,
    countdown: 3,
    
    // SAI计算结果
    saiScore: 0,
    saiLevel: '',
    saiLevelClass: '',
    
    // 音频相关
    audioContext: null,
    audioFormat: 'mp3' // 音频格式配置：默认使用MP3格式
  },

  onLoad: function(options) {
    console.log('🎧 听音辩图页面加载');
    console.log('📊 初始数据状态:', {
      noiseLevel: this.data.noiseLevel,
      isEnvironmentOk: this.data.isEnvironmentOk,
      noiseThreshold: this.data.noiseThreshold
    });

    this.initAudio();
    this.checkAudioFiles();

    // 先使用模拟检测，等用户点击开始测试时再申请权限
    this.startSimulatedDetection();
  },

  onShow: function() {
    console.log('📱 页面显示，检查环境监测状态');
    // 如果环境监测没有启动，重新启动
    if (!this.environmentTimer) {
      console.log('🔄 重新启动环境监测');
      this.startEnvironmentMonitoring();
    }
  },

  onHide: function() {
    console.log('📱 页面隐藏，暂停环境监测');
    this.stopEnvironmentMonitoring();
  },

  onUnload: function() {
    console.log('📱 页面卸载，清理资源');
    // 清理资源
    if (this.data.audioContext) {
      try {
        this.data.audioContext.destroy();
      } catch (error) {
        console.warn('音频上下文销毁失败:', error);
      }
    }
    this.stopEnvironmentMonitoring();
  },

  // 检查音频文件配置
  checkAudioFiles: function() {
    console.log('检查音频文件配置...');

    // 检查音频映射配置是否加载
    if (wordMapping.ALL_WORDS && wordMapping.ALL_WORDS.length > 0) {
      console.log(`音频配置已加载，共${wordMapping.ALL_WORDS.length}个词汇`);
      this.setData({ audioFilesReady: true });
    } else {
      console.error('音频配置未找到');
      this.setData({ audioFilesReady: false });
      wx.showToast({
        title: '音频配置加载失败',
        icon: 'none'
      });
    }
  },



  // 初始化音频上下文
  initAudio: function() {
    const audioContext = wx.createInnerAudioContext();
    this.setData({ audioContext });
  },





  // 开始环境监测
  startEnvironmentMonitoring: function() {
    console.log('🔊 开始环境监测...');

    // 初始化计数器
    this.environmentStableCount = 0;

    // 首先尝试申请麦克风权限
    this.requestMicrophonePermission();
  },

  // 申请麦克风权限
  requestMicrophonePermission: function() {
    console.log('🎤 申请麦克风权限...');

    wx.authorize({
      scope: 'scope.record',
      success: () => {
        console.log('✅ 麦克风权限获取成功');
        this.setData({
          microphoneAuthorized: true,
          isRealDetection: true
        });
        this.startRealEnvironmentDetection();
      },
      fail: () => {
        console.log('❌ 麦克风权限被拒绝，使用模拟检测');
        this.setData({
          microphoneAuthorized: false,
          isRealDetection: false
        });
        this.showPermissionDialog();
      }
    });
  },

  // 显示权限说明对话框
  showPermissionDialog: function() {
    wx.showModal({
      title: '麦克风权限',
      content: '为了准确检测环境噪音，需要使用麦克风权限。您可以在设置中手动开启，或使用模拟检测继续测试。',
      confirmText: '去设置',
      cancelText: '模拟检测',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.record']) {
                console.log('✅ 用户在设置中开启了麦克风权限');
                this.setData({
                  microphoneAuthorized: true,
                  isRealDetection: true
                });
                this.startRealEnvironmentDetection();
              } else {
                console.log('❌ 用户未开启麦克风权限，使用模拟检测');
                this.startSimulatedDetection();
              }
            }
          });
        } else {
          console.log('📱 用户选择使用模拟检测');
          this.startSimulatedDetection();
        }
      }
    });
  },

  // 开始真实环境检测
  startRealEnvironmentDetection: function() {
    console.log('🎤 开始真实环境检测...');

    try {
      // 创建录音管理器
      this.recorderManager = wx.getRecorderManager();

      // 录音配置
      const options = {
        duration: 1000, // 每次录音1秒
        sampleRate: 16000, // 采样率
        numberOfChannels: 1, // 单声道
        encodeBitRate: 48000, // 编码码率
        format: 'mp3', // 音频格式
        frameSize: 10 // 指定帧大小，单位 KB
      };

      // 监听录音开始
      this.recorderManager.onStart(() => {
        console.log('🎤 录音开始');
      });

      // 监听录音结束
      this.recorderManager.onStop((res) => {
        console.log('🎤 录音结束');
        this.analyzeAudioLevel(res);
      });

      // 监听录音错误
      this.recorderManager.onError((err) => {
        console.error('🎤 录音错误:', err);
        this.fallbackToSimulation();
      });

      // 开始第一次录音
      this.startRecording(options);

      // 每2秒进行一次录音检测
      this.environmentTimer = setInterval(() => {
        this.startRecording(options);
      }, 2000);

    } catch (error) {
      console.error('❌ 真实环境检测启动失败:', error);
      this.fallbackToSimulation();
    }
  },

  // 开始录音
  startRecording: function(options) {
    try {
      if (this.recorderManager) {
        this.recorderManager.start(options);
      }
    } catch (error) {
      console.error('❌ 录音启动失败:', error);
      this.fallbackToSimulation();
    }
  },

  // 分析音频音量级别
  analyzeAudioLevel: function(audioData) {
    try {
      console.log('🔊 分析音频数据...');

      // 这里需要分析音频数据来计算分贝
      // 由于微信小程序限制，我们使用文件大小作为音量的粗略估计
      const fileSize = audioData.fileSize || 0;
      const duration = audioData.duration || 1000;

      // 基于文件大小估算音量（这是一个简化的方法）
      const sizePerSecond = fileSize / (duration / 1000);

      // 将文件大小映射到分贝值（经验公式）
      let estimatedDB = 0;
      if (sizePerSecond < 1000) {
        estimatedDB = 25 + (sizePerSecond / 1000) * 10; // 25-35dB
      } else if (sizePerSecond < 5000) {
        estimatedDB = 35 + ((sizePerSecond - 1000) / 4000) * 15; // 35-50dB
      } else {
        estimatedDB = 50 + Math.min(((sizePerSecond - 5000) / 5000) * 20, 20); // 50-70dB
      }

      // 添加一些随机变化使其更真实
      estimatedDB += (Math.random() - 0.5) * 4;

      const roundedDB = Math.round(estimatedDB);
      const isEnvironmentOk = roundedDB < this.data.noiseThreshold;

      console.log(`📊 真实环境检测: 文件大小=${fileSize}字节, 估算=${roundedDB}dB, 阈值=${this.data.noiseThreshold}dB, 状态=${isEnvironmentOk ? '良好' : '嘈杂'}`);

      this.setData({
        noiseLevel: roundedDB,
        isEnvironmentOk: isEnvironmentOk
      });

      // 环境稳定性检测
      if (isEnvironmentOk) {
        this.environmentStableCount = (this.environmentStableCount || 0) + 1;
      } else {
        this.environmentStableCount = 0;
      }

    } catch (error) {
      console.error('❌ 音频分析失败:', error);
      this.fallbackToSimulation();
    }
  },

  // 降级到模拟检测
  fallbackToSimulation: function() {
    console.log('🔄 降级到模拟环境检测');
    this.setData({
      isRealDetection: false,
      microphoneAuthorized: false
    });
    this.startSimulatedDetection();
  },

  // 开始模拟检测
  startSimulatedDetection: function() {
    console.log('🎭 开始模拟环境检测...');

    // 立即执行一次检测
    this.updateSimulatedEnvironmentStatus();

    // 每2秒更新一次环境状态
    this.environmentTimer = setInterval(() => {
      this.updateSimulatedEnvironmentStatus();
    }, 2000);
  },

  // 更新模拟环境状态
  updateSimulatedEnvironmentStatus: function() {
    try {
      console.log('🎭 更新模拟环境状态...');

      // 模拟环境噪音监测，偏向于良好环境
      const baseNoise = 30; // 基础噪音30dB
      const randomVariation = Math.random() * 15; // 0-15dB的随机变化
      const simulatedNoise = baseNoise + randomVariation; // 30-45dB范围

      const roundedNoise = Math.round(simulatedNoise);
      const isEnvironmentOk = roundedNoise < this.data.noiseThreshold;

      console.log(`📊 模拟环境检测: ${roundedNoise}dB, 阈值: ${this.data.noiseThreshold}dB, 状态: ${isEnvironmentOk ? '良好' : '嘈杂'}`);

      this.setData({
        noiseLevel: roundedNoise,
        isEnvironmentOk: isEnvironmentOk
      });

      // 环境稳定性检测
      if (isEnvironmentOk) {
        this.environmentStableCount = (this.environmentStableCount || 0) + 1;
      } else {
        this.environmentStableCount = 0;
      }

    } catch (error) {
      console.error('❌ 模拟环境状态更新失败:', error);
      this.setData({
        noiseLevel: 35,
        isEnvironmentOk: true
      });
    }
  },



  // 停止环境监测
  stopEnvironmentMonitoring: function() {
    console.log('🛑 停止环境监测');

    // 清理定时器
    if (this.environmentTimer) {
      clearInterval(this.environmentTimer);
      this.environmentTimer = null;
    }

    // 停止录音
    if (this.recorderManager) {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.warn('录音停止失败:', error);
      }
      this.recorderManager = null;
    }

    this.environmentStableCount = 0;
  },

  // 开始测试
  startTest: function() {
    // 首先尝试申请麦克风权限以获得更准确的环境检测
    this.requestMicrophonePermissionForTest();
  },

  // 为测试申请麦克风权限
  requestMicrophonePermissionForTest: function() {
    console.log('🎤 为听音辩图测试申请麦克风权限...');

    wx.authorize({
      scope: 'scope.record',
      success: () => {
        console.log('✅ 麦克风权限获取成功，切换到真实环境检测');
        this.setData({
          microphoneAuthorized: true,
          isRealDetection: true
        });

        // 停止模拟检测，启动真实检测
        this.stopEnvironmentMonitoring();
        this.startRealEnvironmentDetection();

        // 等待1秒让真实检测稳定，然后继续测试流程
        setTimeout(() => {
          this.proceedWithTest();
        }, 1000);
      },
      fail: () => {
        console.log('❌ 麦克风权限被拒绝，继续使用模拟检测');
        // 权限被拒绝，直接使用当前的模拟检测结果继续测试
        this.proceedWithTest();
      }
    });
  },

  // 继续测试流程
  proceedWithTest: function() {
    if (!this.data.isEnvironmentOk) {
      wx.showModal({
        title: '环境检测',
        content: `当前环境噪音${this.data.noiseLevel}dB，超过${this.data.noiseThreshold}dB阈值，请在安静环境中进行测试`,
        showCancel: false,
        confirmText: '重新检测',
        success: (res) => {
          if (res.confirm) {
            // 用户选择重新检测，重新启动环境检测
            if (this.data.isRealDetection) {
              this.startRealEnvironmentDetection();
            } else {
              this.startSimulatedDetection();
            }
          }
        }
      });
      return;
    }

    if (!this.data.audioFilesReady) {
      wx.showToast({
        title: '音频配置未就绪',
        icon: 'none'
      });
      return;
    }

    console.log('🎯 开始听音辩图测试');

    this.setData({
      testStatus: 'testing',
      showInstructions: false,
      currentLevel: 0,
      currentWord: 0,
      testResults: [],
      completedSteps: 0 // 重置进度为0
    });

    this.startLevelTest();
  },

  // 开始某个声强级别的测试
  startLevelTest: function() {
    const { currentLevel, soundLevels } = this.data;

    console.log(`🎯 开始 ${soundLevels[currentLevel].name} 测试`);

    // 从对应的测试级别获取词汇
    const levelWords = wordMapping.TEST_LEVELS[soundLevels[currentLevel].level];

    if (!levelWords || levelWords.length === 0) {
      console.error(`❌ ${soundLevels[currentLevel].name} 级别没有可用词库`);
      wx.showModal({
        title: '测试错误',
        content: `${soundLevels[currentLevel].name} 级别暂无可用词汇，请联系管理员`,
        showCancel: false
      });
      return;
    }

    console.log(`📚 可用词汇: ${levelWords.length}个 - ${levelWords.join(', ')}`);

    // 随机选择2个词语进行测试
    const shuffledWords = [...levelWords].sort(() => Math.random() - 0.5);
    const selectedWords = shuffledWords.slice(0, 2);

    // 为选中的词汇分配性别（第一个男声，第二个女声）
    const wordsWithGender = selectedWords.map((word, index) => {
      const gender = index === 0 ? 'male' : 'female';
      const audioPath = this.getAudioPath(word, gender);
      const imagePath = this.getImagePath(word);

      return {
        word: word,
        gender: gender,
        audioPath: audioPath,
        imagePath: imagePath,
        resourceReady: audioPath !== null && imagePath !== null
      };
    });

    console.log(`🎲 选中的测试词汇:`, wordsWithGender);

    // 检查资源是否准备好
    const allResourcesReady = wordsWithGender.every(item => item.resourceReady);
    if (!allResourcesReady) {
      wx.showModal({
        title: '资源准备中',
        content: '测试资源正在后台下载，请稍候再试',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    this.setData({
      currentWords: wordsWithGender,
      currentWord: 0,
      currentLevelResults: []
    });

    this.playCurrentWord();
  },

  // 播放当前词语
  playCurrentWord: function() {
    const { currentWords, currentWord, soundLevels, currentLevel, audioFilesReady } = this.data;

    if (currentWord >= currentWords.length) {
      this.completeLevelTest();
      return;
    }

    const wordData = currentWords[currentWord];
    const wordText = wordData.word || wordData; // 兼容旧格式
    const wordGender = wordData.gender || 'male'; // 默认男声
    const soundLevel = soundLevels[currentLevel];

    console.log(`🎵 播放音频 (${wordGender === 'female' ? '女声' : '男声'})`);

    // 生成图片选项
    const imageOptionsData = wordMapping.generateImageOptions(wordText);

    this.setData({
      currentWordText: wordText,
      currentWordGender: wordGender,
      isPlaying: true,
      userResponse: '',
      imageOptions: imageOptionsData.options,
      correctImageIndex: imageOptionsData.correctIndex,
      selectedImageIndex: -1
    });

    // 使用本地音频文件
    if (audioFilesReady) {
      this.playLocalAudio(wordText, soundLevel.level, wordGender);
    } else {
      console.error('音频配置未就绪');
      wx.showToast({
        title: '音频系统未就绪',
        icon: 'none'
      });
    }
  },



  // 播放本地音频文件（支持男声和女声）
  playLocalAudio: function(text, soundLevel, gender = 'male') {
    try {
      // 优先使用本地缓存的音频文件
      const audioPath = this.getAudioPath(text, gender);

      if (audioPath) {
        // 使用新的音量系数计算方法（以85dB为基准）
        const volume = wordMapping.getVolumeCoefficient(soundLevel);
        const genderText = gender === 'female' ? '女声' : '男声';
        console.log(`✅ 播放${genderText}音频文件 (目标${soundLevel}dB)`);
        console.log(`🔊 音量系数: ${volume.toFixed(3)} (基准85dB)`);

        this.playAudio(audioPath, volume);
      } else {
        const genderText = gender === 'female' ? '女声' : '男声';
        console.log(`⏳ ${genderText}音频资源正在下载中...`);
        wx.showToast({
          title: '资源准备中，请稍候',
          icon: 'loading',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('❌ 播放本地音频失败:', error);
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
    }
  },



  // 获取音频路径（优先使用本地缓存）
  getAudioPath: function(word, gender) {
    try {
      // 尝试获取本地缓存路径
      const localPath = resourceManager.getLocalResourcePath(word, 'audio', gender);

      // 检查本地文件是否存在
      try {
        wx.getFileSystemManager().statSync(localPath);
        console.log('使用本地音频:', localPath);
        return localPath;
      } catch (error) {
        // 本地缓存不存在，返回null表示资源未准备好
        console.log('音频资源未准备好:', word, gender);
        return null;
      }
    } catch (error) {
      console.error('获取音频路径失败:', error);
      return null;
    }
  },

  // 获取图片路径（优先使用本地缓存）
  getImagePath: function(word) {
    try {
      // 尝试获取本地缓存路径
      const localPath = resourceManager.getLocalResourcePath(word, 'image');

      // 检查本地文件是否存在
      try {
        wx.getFileSystemManager().statSync(localPath);
        console.log('使用本地图片:', localPath);
        return localPath;
      } catch (error) {
        // 本地缓存不存在，返回null表示资源未准备好
        console.log('图片资源未准备好:', word);
        return null;
      }
    } catch (error) {
      console.error('获取图片路径失败:', error);
      return null;
    }
  },

  // 播放音频
  playAudio: function(audioUrl, volume = 1.0) {
    const { audioContext } = this.data;

    if (!audioContext) {
      console.error('音频上下文未初始化');
      wx.showToast({
        title: '音频系统未就绪',
        icon: 'none'
      });
      return;
    }

    try {
      // 清除之前的事件监听器
      audioContext.offPlay();
      audioContext.offEnded();
      audioContext.offError();

      // 设置音频源和音量
      audioContext.src = audioUrl;
      audioContext.volume = volume;

      console.log(`设置音频音量: ${volume.toFixed(3)} (${(volume * 100).toFixed(1)}%)`);

      // 设置事件监听器
      audioContext.onPlay(() => {
        console.log('音频开始播放');
      });

      audioContext.onEnded(() => {
        console.log('音频播放结束');
        this.setData({ isPlaying: false });
        // 音频播放结束后，用户可以选择图片
      });

      audioContext.onError((err) => {
        console.error('音频播放错误', err);
        this.setData({ isPlaying: false });
        wx.showToast({
          title: '音频播放失败',
          icon: 'none'
        });
      });

      // 播放音频
      audioContext.play();
    } catch (error) {
      console.error('音频播放异常:', error);
      this.setData({ isPlaying: false });
      wx.showToast({
        title: '音频播放异常',
        icon: 'none'
      });
    }
  },

  // 选择图片
  selectImage: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ selectedImageIndex: index });
  },

  // 确认选择
  confirmSelection: function() {
    const { selectedImageIndex, correctImageIndex, imageOptions, isConfirming } = this.data;

    // 防重复点击
    if (isConfirming) {
      console.log('⚠️ 正在处理中，请勿重复点击');
      return;
    }

    if (selectedImageIndex === -1) {
      wx.showToast({
        title: '请先选择一个图片',
        icon: 'none'
      });
      return;
    }

    // 设置确认状态，防止重复点击
    this.setData({ isConfirming: true });

    const selectedWord = imageOptions[selectedImageIndex].word;
    const isCorrect = selectedImageIndex === correctImageIndex;

    console.log('✅ 确认选择:', {
      selectedWord,
      isCorrect,
      selectedIndex: selectedImageIndex,
      correctIndex: correctImageIndex
    });

    this.setData({ userResponse: selectedWord });
    this.recordResponse(selectedWord, isCorrect);
  },

  // 检查响应正确性
  checkResponse: function(response) {
    const { currentWordText } = this.data;
    const isCorrect = response === currentWordText;
    
    this.recordResponse(response, isCorrect);
  },

  // 记录响应结果
  recordResponse: function(response, isCorrect) {
    console.log('🎯 记录用户回答:', {
      word: this.data.currentWordText,
      gender: this.data.currentWordGender,
      response: response,
      isCorrect: isCorrect,
      currentLevel: this.data.currentLevel,
      currentWord: this.data.currentWord
    });

    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
    }

    const { currentLevelResults, currentWordText, currentWordGender, soundLevels, currentLevel } = this.data;

    const result = {
      word: currentWordText,
      gender: currentWordGender,
      response: response,
      isCorrect: isCorrect,
      soundLevel: soundLevels[currentLevel].level,
      timestamp: new Date().getTime()
    };

    currentLevelResults.push(result);

    console.log('📝 当前级别结果数组:', currentLevelResults);
    console.log('📊 当前级别结果数量:', currentLevelResults.length);

    // 用户提交答案后，进度+1
    const newCompletedSteps = this.data.completedSteps + 1;

    this.setData({
      currentLevelResults,
      currentWord: this.data.currentWord + 1,
      completedSteps: newCompletedSteps,
      isConfirming: false, // 重置确认状态
      selectedImageIndex: -1 // 重置选择状态
    });

    console.log('⏭️ 下一个词语索引:', this.data.currentWord);
    console.log('📚 当前级别总词语数:', this.data.currentWords.length);
    console.log('📈 完成步骤数:', newCompletedSteps, '/', this.data.totalSteps);

    // 短暂显示结果后继续下一个词语
    setTimeout(() => {
      this.playCurrentWord();
    }, 1000);
  },

  // 完成当前级别测试
  completeLevelTest: function() {
    const { testResults, currentLevelResults, currentLevel, soundLevels } = this.data;

    console.log('完成级别测试 - 当前级别:', currentLevel);
    console.log('当前级别结果:', currentLevelResults);
    console.log('声音级别配置:', soundLevels[currentLevel]);

    // 计算当前级别识别率
    const correctCount = currentLevelResults.filter(r => r.isCorrect).length;
    const totalCount = currentLevelResults.length;
    const recognitionRate = totalCount > 0 ? (correctCount / totalCount) * 100 : 0;

    console.log('计算结果:', {
      correctCount,
      totalCount,
      recognitionRate
    });

    const levelResult = {
      level: soundLevels[currentLevel].level,
      results: currentLevelResults,
      recognitionRate: recognitionRate,
      recognitionRateFormatted: recognitionRate.toFixed(1), // 添加格式化后的识别率
      correctCount: correctCount,
      totalCount: totalCount
    };

    console.log('级别结果对象:', levelResult);

    testResults.push(levelResult);

    this.setData({ testResults });

    console.log('更新后的所有测试结果:', this.data.testResults);
    
    // 检查是否完成所有级别
    if (currentLevel < soundLevels.length - 1) {
      // 继续下一个级别，不显示弹窗
      this.setData({
        currentLevel: currentLevel + 1,
        currentLevelResults: []
      });
      this.startLevelTest();
    } else {
      // 完成所有测试
      this.completeTest();
    }
  },

  // 完成整个测试
  completeTest: function() {
    this.setData({ testStatus: 'completed' });
    this.calculateSAI();
    this.saveTestResult();
  },

  // 计算SAI指数
  calculateSAI: function() {
    const { testResults } = this.data;

    if (testResults.length === 0) return;

    // SAI指数 = (55dB识别率 + 70dB识别率 + 85dB识别率) / 3
    const totalRate = testResults.reduce((sum, result) => sum + result.recognitionRate, 0);
    const saiScore = totalRate / testResults.length;

    let saiLevel = '';
    let saiLevelClass = '';
    if (saiScore >= 90) {
      saiLevel = '优秀';
      saiLevelClass = 'excellent';
    } else if (saiScore >= 75) {
      saiLevel = '良好';
      saiLevelClass = 'good';
    } else if (saiScore >= 60) {
      saiLevel = '需关注';
      saiLevelClass = 'attention';
    } else {
      saiLevel = '建议专业检测';
      saiLevelClass = 'professional';
    }

    this.setData({
      saiScore: saiScore.toFixed(1),
      saiLevel: saiLevel,
      saiLevelClass: saiLevelClass
    });
  },

  // 保存测试结果
  saveTestResult: function() {
    const { testResults, saiScore, saiLevel, saiLevelClass } = this.data;

    wx.cloud.callFunction({
      name: 'saveTestResult',
      data: {
        testResults: testResults,
        saiScore: parseFloat(saiScore),
        saiLevel: saiLevel,
        saiLevelClass: saiLevelClass,
        testDate: new Date(),
        testType: 'hearing_sai'
      }
    }).then(res => {
      console.log('测试结果保存成功', res);
    }).catch(err => {
      console.error('测试结果保存失败', err);
    });
  },

  // 重新测试
  retryTest: function() {
    this.setData({
      testStatus: 'ready',
      currentLevel: 0,
      currentWord: 0,
      testResults: [],
      currentLevelResults: [],
      saiScore: 0,
      saiLevel: '',
      saiLevelClass: '',
      showInstructions: true
    });
  },

  // 查看详细结果
  viewDetailResults: function() {
    console.log('准备跳转到结果页面，测试结果数据:', this.data.testResults);

    // 验证数据完整性
    this.data.testResults.forEach((result, index) => {
      console.log(`级别 ${index + 1}:`, {
        level: result.level,
        recognitionRate: result.recognitionRate,
        correctCount: result.correctCount,
        totalCount: result.totalCount
      });
    });

    // 使用本地存储传递数据，避免URL长度限制
    try {
      wx.setStorageSync('testResults', this.data.testResults);
      console.log('测试结果已保存到本地存储');

      wx.navigateTo({
        url: '/pages/test-result/test-result'
      });
    } catch (e) {
      console.error('保存测试结果失败:', e);
      wx.showToast({
        title: '数据传递失败',
        icon: 'none'
      });
    }
  },

  // 返回首页
  goHome: function() {
    wx.navigateBack();
  }
});
