// 听力测试词汇映射配置
// 统一管理音频和图片文件，通过文件名直接匹配
// 音频文件存储在 miniprogram/audio/ 目录下，以85dB为基准
// 图片文件存储在 miniprogram/images/words/ 目录下

// 所有可用的词汇列表（基于实际音频文件）
const ALL_WORDS = [
  '书本', '冰箱', '回家', '大象', '太阳', '山脉', '帽子', '报纸',
  '时间', '月亮', '桌子', '气球', '汽车', '河流', '熊猫', '电视',
  '电话', '窗户', '自行车', '花朵', '苹果', '蛋糕', '衣服', '铅笔', '鞋子'
];

// 词语分类映射，用于生成干扰项
const WORD_CATEGORIES = {
  '物品': ['书本', '帽子', '报纸', '桌子', '气球', '电视', '电话', '窗户', '自行车', '蛋糕', '衣服', '铅笔', '鞋子'],
  '电器': ['冰箱', '电视', '电话'],
  '交通工具': ['汽车', '自行车'],
  '动物': ['大象', '熊猫'],
  '自然': ['太阳', '山脉', '月亮', '河流', '花朵'],
  '食物': ['苹果', '蛋糕'],
  '抽象': ['回家', '时间']
};

// 测试分贝级别配置（用于测试逻辑，不影响文件路径）
const TEST_LEVELS = {
  55: ALL_WORDS.slice(0, 8),   // 前8个词汇用于55dB测试
  70: ALL_WORDS.slice(8, 16),  // 中间8个词汇用于70dB测试
  85: ALL_WORDS.slice(16, 25)  // 最后9个词汇用于85dB测试
};

// ===== 文件路径获取函数 =====

// 获取音频文件路径（支持男声和女声）
function getAudioPath(word, gender = 'male') {
  if (!ALL_WORDS.includes(word)) {
    return null;
  }
  const genderDir = gender === 'female' ? '女声' : '男声';
  return `audio/${genderDir}/${word}.mp3`;
}

// 获取图片文件路径
function getImagePath(word) {
  if (!ALL_WORDS.includes(word)) {
    return null;
  }
  return `/images/wordlist/${word}.png`;
}

// ===== 词汇管理函数 =====

// 获取所有词汇列表
function getAllWords() {
  return ALL_WORDS.map(word => ({
    word: word,
    audioPath: getAudioPath(word),
    imagePath: getImagePath(word),
    soundLevel: 85 // 所有文件都以85dB为基准
  }));
}

// 获取指定分贝级别的测试词汇
function getWordsByLevel(soundLevel) {
  return TEST_LEVELS[soundLevel] || [];
}

// 随机选择指定数量的词汇
function getRandomWords(soundLevel, count = 2) {
  const words = getWordsByLevel(soundLevel);

  if (words.length <= count) {
    return words;
  }

  // 随机打乱数组
  const shuffled = words.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 随机选择性别（每个分贝级别一半男声，一半女声）
function getRandomGender(wordIndex, totalWords) {
  // 前一半使用男声，后一半使用女声
  return wordIndex < Math.floor(totalWords / 2) ? 'male' : 'female';
}

// 为测试词汇分配性别
function assignGenderToWords(words) {
  return words.map((word, index) => {
    const gender = getRandomGender(index, words.length);
    return {
      word: word,
      gender: gender,
      audioPath: getAudioPath(word, gender),
      imagePath: getImagePath(word)
    };
  });
}

// 获取词语所属的分类
function getWordCategory(word) {
  for (const category in WORD_CATEGORIES) {
    if (WORD_CATEGORIES[category].includes(word)) {
      return category;
    }
  }
  return null;
}

// ===== 测试选项生成函数 =====

// 生成4个选项（1个正确答案 + 3个干扰项）
function generateImageOptions(correctWord) {
  const options = [correctWord];
  const category = getWordCategory(correctWord);

  if (category) {
    // 优先从同类别中选择干扰项
    const categoryWords = WORD_CATEGORIES[category].filter(word => word !== correctWord);

    // 随机选择2个同类别的干扰项
    const shuffledCategoryWords = categoryWords.sort(() => Math.random() - 0.5);
    options.push(...shuffledCategoryWords.slice(0, 2));

    // 如果同类别词语不够，从其他类别补充
    if (options.length < 4) {
      const allWords = ALL_WORDS.filter(word => !options.includes(word));
      const shuffledAllWords = allWords.sort(() => Math.random() - 0.5);
      options.push(...shuffledAllWords.slice(0, 4 - options.length));
    }
  } else {
    // 如果找不到分类，随机选择3个其他词语
    const allWords = ALL_WORDS.filter(word => word !== correctWord);
    const shuffledWords = allWords.sort(() => Math.random() - 0.5);
    options.push(...shuffledWords.slice(0, 3));
  }

  // 打乱选项顺序
  const shuffledOptions = options.sort(() => Math.random() - 0.5);

  // 返回选项数组，包含图片路径和正确答案索引
  return {
    options: shuffledOptions.map(word => ({
      word: word,
      imagePath: getImagePath(word)
    })),
    correctIndex: shuffledOptions.indexOf(correctWord)
  };
}

// ===== 音频相关函数 =====

// 计算音量系数（以85dB为基准）
function getVolumeCoefficient(targetDB) {
  // 85dB为基准音量（系数1.0）
  if (targetDB >= 85) {
    return 1.0; // 85dB及以上保持原音量
  } else if (targetDB >= 70) {
    return 0.6; // 70dB约为85dB的60%音量
  } else if (targetDB >= 55) {
    return 0.3; // 55dB约为85dB的30%音量
  } else {
    return 0.1; // 更低分贝
  }
}

// ===== 文件检查函数 =====

// 检查词汇是否存在
function checkWordExists(word) {
  return ALL_WORDS.includes(word);
}

module.exports = {
  // 数据
  ALL_WORDS,
  WORD_CATEGORIES,
  TEST_LEVELS,
  
  // 路径获取
  getAudioPath,
  getImagePath,
  
  // 词汇管理
  getAllWords,
  getWordsByLevel,
  getRandomWords,
  getWordCategory,

  // 性别分配
  getRandomGender,
  assignGenderToWords,
  
  // 选项生成
  generateImageOptions,
  
  // 音频相关
  getVolumeCoefficient,
  
  // 检查函数
  checkWordExists
};
