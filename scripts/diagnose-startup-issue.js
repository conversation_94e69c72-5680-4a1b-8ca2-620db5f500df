// 诊断启动问题的脚本
const fs = require('fs');
const path = require('path');

console.log('🔍 诊断启动问题');
console.log('='.repeat(50));

// 1. 检查当前工作目录
console.log('\n📁 当前工作目录:');
console.log('process.cwd():', process.cwd());

// 2. 检查项目配置文件
console.log('\n📋 检查项目配置文件:');

const configFiles = [
  'project.config.json',
  'miniprogram/project.config.json',
  'project.private.config.json',
  'miniprogram/project.private.config.json',
  'cloudbaserc.json'
];

configFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${file} - 存在 (${stats.size} bytes)`);
      
      // 读取内容检查关键字段
      if (file.endsWith('.json')) {
        try {
          const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          if (content.appid) {
            console.log(`   AppID: ${content.appid}`);
          }
          if (content.projectname) {
            console.log(`   项目名: ${content.projectname}`);
          }
          if (content.miniprogramRoot) {
            console.log(`   小程序根目录: ${content.miniprogramRoot}`);
          }
          if (content.cloudfunctionRoot) {
            console.log(`   云函数根目录: ${content.cloudfunctionRoot}`);
          }
        } catch (parseError) {
          console.log(`   ⚠️ JSON解析失败: ${parseError.message}`);
        }
      }
    } else {
      console.log(`❌ ${file} - 不存在`);
    }
  } catch (error) {
    console.log(`❌ ${file} - 检查失败: ${error.message}`);
  }
});

// 3. 检查目录结构
console.log('\n📂 检查目录结构:');

const directories = [
  'miniprogram',
  'cloudfunctions',
  'miniprogram/pages',
  'miniprogram/utils',
  'cloudfunctions/getResourceList'
];

directories.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  try {
    if (fs.existsSync(dirPath)) {
      const stats = fs.statSync(dirPath);
      if (stats.isDirectory()) {
        const files = fs.readdirSync(dirPath);
        console.log(`✅ ${dir} - 存在 (${files.length} 个文件/目录)`);
      } else {
        console.log(`⚠️ ${dir} - 存在但不是目录`);
      }
    } else {
      console.log(`❌ ${dir} - 不存在`);
    }
  } catch (error) {
    console.log(`❌ ${dir} - 检查失败: ${error.message}`);
  }
});

// 4. 检查关键文件
console.log('\n📄 检查关键文件:');

const keyFiles = [
  'miniprogram/app.js',
  'miniprogram/app.json',
  'miniprogram/utils/resourceManager.js',
  'cloudfunctions/getResourceList/index.js'
];

keyFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${file} - 存在 (${stats.size} bytes)`);
    } else {
      console.log(`❌ ${file} - 不存在`);
    }
  } catch (error) {
    console.log(`❌ ${file} - 检查失败: ${error.message}`);
  }
});

// 5. 检查环境变量和路径
console.log('\n🌍 环境信息:');
console.log('NODE_VERSION:', process.version);
console.log('PLATFORM:', process.platform);
console.log('ARCH:', process.arch);

// 6. 检查是否有旧项目路径的引用
console.log('\n🔍 检查可能的路径问题:');

const possibleConfigFiles = [
  '.vscode/settings.json',
  '.idea/workspace.xml',
  'package.json'
];

possibleConfigFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('hearing1') || content.includes('cloudbase-miniprogram-hearing1')) {
        console.log(`⚠️ ${file} - 可能包含旧项目路径引用`);
      } else {
        console.log(`✅ ${file} - 无旧路径引用`);
      }
    }
  } catch (error) {
    // 忽略读取错误
  }
});

// 7. 生成解决方案
console.log('\n💡 可能的解决方案:');
console.log('1. 清理IDE缓存:');
console.log('   - 关闭微信开发者工具');
console.log('   - 删除 .vscode 或 .idea 目录（如果存在）');
console.log('   - 重新打开项目');

console.log('\n2. 检查微信开发者工具设置:');
console.log('   - 确保项目路径指向正确的目录');
console.log('   - 检查项目设置中的路径配置');

console.log('\n3. 重新初始化项目:');
console.log('   - 在微信开发者工具中重新导入项目');
console.log('   - 选择当前目录作为项目根目录');

console.log('\n4. 检查文件权限:');
console.log('   - 确保当前用户有读写权限');
console.log('   - 检查文件是否被其他程序占用');

console.log('\n📋 诊断完成！');
console.log('如果问题仍然存在，请检查微信开发者工具的项目设置。');
