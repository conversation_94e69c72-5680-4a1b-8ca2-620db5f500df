// 测试新的下载方法
console.log('🧪 测试新的 wx.cloud.downloadFile 下载方法');

const testCode = `
// 在微信开发者工具控制台中执行
console.log('🔍 测试新的下载方法...');

// 1. 测试获取资源列表（应该包含 fileID）
const resourceManager = require('./utils/resourceManager');

resourceManager.getResourceList().then(resourceList => {
  console.log('📋 资源列表获取成功:', resourceList);
  
  if (resourceList.audio && resourceList.audio.length > 0) {
    const firstAudio = resourceList.audio[0];
    console.log('🎵 第一个音频资源:', firstAudio);
    console.log('📁 fileID:', firstAudio.fileID);
    console.log('📁 cloudPath:', firstAudio.cloudPath);
    
    // 2. 测试直接使用 wx.cloud.downloadFile
    if (firstAudio.fileID) {
      console.log('\\n🧪 测试 wx.cloud.downloadFile...');
      
      wx.cloud.downloadFile({
        fileID: firstAudio.fileID,
        success: (res) => {
          console.log('✅ wx.cloud.downloadFile 成功:', res);
          console.log('📄 临时文件路径:', res.tempFilePath);
          
          // 检查临时文件是否存在
          try {
            const stat = wx.getFileSystemManager().statSync(res.tempFilePath);
            console.log('📊 临时文件信息:', stat);
          } catch (error) {
            console.error('❌ 临时文件不存在:', error);
          }
        },
        fail: (error) => {
          console.error('❌ wx.cloud.downloadFile 失败:', error);
        }
      });
    } else {
      console.error('❌ 资源没有 fileID 字段');
    }
  }
  
  if (resourceList.image && resourceList.image.length > 0) {
    const firstImage = resourceList.image[0];
    console.log('🖼️ 第一个图片资源:', firstImage);
    console.log('📁 fileID:', firstImage.fileID);
  }
}).catch(error => {
  console.error('❌ 获取资源列表失败:', error);
});

// 3. 测试完整的下载流程
console.log('\\n🔄 测试完整下载流程...');

resourceManager.setProgressCallback((progress, downloaded, total) => {
  console.log(\`📊 下载进度: \${progress.toFixed(1)}% (\${downloaded}/\${total})\`);
});

resourceManager.setCompleteCallback((message) => {
  if (message) {
    console.log('⚠️ 下载完成（部分失败）:', message);
  } else {
    console.log('✅ 下载完成');
  }
});

// 清理缓存状态，强制重新下载
wx.removeStorageSync('audio_cache_status');
wx.removeStorageSync('image_cache_status');

resourceManager.init().then(() => {
  console.log('✅ 资源初始化完成');
}).catch(error => {
  console.error('❌ 资源初始化失败:', error);
});

// 4. 测试单个文件下载
console.log('\\n📁 测试单个文件下载...');

// 构造一个测试资源对象
const testResource = {
  word: '书本',
  gender: 'female',
  fileID: 'cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1314700961/resources/audio/female/书本.mp3'
};

resourceManager.downloadSingleResource(testResource, 'audio').then(localPath => {
  console.log('✅ 单个文件下载成功:', localPath);
  
  // 检查本地文件是否存在
  try {
    const stat = wx.getFileSystemManager().statSync(localPath);
    console.log('📊 本地文件信息:', stat);
  } catch (error) {
    console.error('❌ 本地文件不存在:', error);
  }
}).catch(error => {
  console.error('❌ 单个文件下载失败:', error);
});
`;

console.log('\n📋 在微信开发者工具控制台中执行以下代码:');
console.log('='.repeat(60));
console.log(testCode);
console.log('='.repeat(60));

console.log('\n🎯 测试目标:');
console.log('1. 验证云函数返回的资源列表包含正确的 fileID');
console.log('2. 测试 wx.cloud.downloadFile 是否能正常工作');
console.log('3. 验证完整的下载流程');
console.log('4. 检查文件是否正确保存到本地');

console.log('\n📝 预期结果:');
console.log('- 资源列表应该包含 fileID 字段');
console.log('- fileID 格式: cloud://envId.xxx/resources/...');
console.log('- wx.cloud.downloadFile 应该返回临时文件路径');
console.log('- 文件应该正确保存到本地缓存目录');

console.log('\n🔍 关键检查点:');
console.log('- fileID 格式是否正确');
console.log('- 临时文件是否成功下载');
console.log('- 文件移动是否成功');
console.log('- 本地文件是否可以访问');

console.log('\n📊 fileID 格式示例:');
console.log('cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1314700961/resources/audio/female/书本.mp3');
console.log('cloud://cloud1-0gjev5gfdef4d262.636c-cloud1-0gjev5gfdef4d262-1314700961/resources/images/wordlist/书本.png');
