# AI听健 - 听音辩图测试小程序

本项目是一个基于微信小程序 + 云开发的AI听力健康应用，通过听音辩图测试提供专业级听力评估，集成AI健康助手功能。

[![Powered by CloudBase](https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/mcp/powered-by-cloudbase-badge.svg)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)  

> 本项目基于 [**CloudBase AI ToolKit**](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit) 开发，通过AI提示词和 MCP 协议+云开发，让开发更智能、更高效，支持AI生成全栈代码、一键部署至腾讯云开发（免服务器）、智能日志修复。

## 项目特点

- 📄 小程序完善的前端开发能力
- 🚀 集成云开发云函数等后端能力
- 🤖 集成 AI IDE 规则，提供智能化开发体验
- ☁️ 集成云开发 MCP，提供一站式云服务开发部署体验
- 🎁 深度集成腾讯云开发 CloudBase，提供一站式后端云服务

## 项目架构

### 🔧 云函数（已部署）
- ✅ `generateTTS`：生成不同声强级别的TTS语音（降级备用）
- ✅ `preGenerateAudio`：预生成音频文件到云存储
- ✅ `getAudioMapping`：获取音频文件映射配置
- ✅ `speechRecognition`：语音识别处理（模拟实现）
- ✅ `saveTestResult`：保存测试结果到数据库
- ✅ `getTestHistory`：获取用户测试历史记录
- ✅ `generateReport`：生成测试报告（模拟实现）
- ✅ `getOpenId`：获取用户的 `openid`、`appid` 和 `unionid`（已存在）

### 🎵 男女声音频文件系统（已更新为MP3格式）
- `miniprogram/audio/男声/`：男声音频文件目录（25个词汇，MP3格式）
- `miniprogram/audio/女声/`：女声音频文件目录（25个词汇，MP3格式）
- `miniprogram/images/wordlist/`：词汇对应图片目录（25个PNG文件）
- `miniprogram/config/word-mapping.js`：词汇映射配置（已更新为MP3格式）
- 性别分配：每个分贝级别一半男声，一半女声
- 音频格式：统一使用MP3格式，提高真机兼容性
- 音量调节：通过系数调节实现不同分贝级别
  - 55dB：30%音量（系数0.3）
  - 70dB：60%音量（系数0.6）
  - 85dB：100%音量（系数1.0）
- `scripts/verify-mp3-config.js`：MP3配置验证脚本
- `scripts/rename-wav-to-mp3.js`：音频文件重命名脚本
- `scripts/convert-wav-to-mp3.js`：音频格式转换脚本（需要ffmpeg）

### 📊 数据库集合
- `hearing_test_results`：存储听力测试结果数据
- `hearing_test_config`：存储音频映射配置（云端备份）

### 📱 小程序页面
- `index`：首页，展示项目介绍和功能入口
- `hearing-test`：听力测试主页面，包含完整测试流程
- `test-result`：测试结果详情页，显示SAI指数和专业建议
- `test-history`：测试历史记录页面

### 🎯 核心功能模块
- **听力测试引擎**：三级声强测试（55/70/85dB）
- **统一音频系统**：85dB基准音频文件 + 音量系数调节
- **图片选择交互**：4选1图片识别替代语音复述
- **环境监测系统**：实时噪音检测和控制
- **SAI计算引擎**：社交够用指数科学计算
- **报告生成系统**：可视化图表和专业建议

### 🧩 自定义组件
- `cloudbase-badge`：CloudBase品牌标识组件

## 开始使用

### 前提条件
- 安装小程序开发工具。
- 拥有腾讯云开发账号。

### 安装依赖
云函数依赖已在 `cloudfunctions/getOpenId/package.json` 中定义，可在云开发控制台中安装依赖。

### 配置云开发环境
项目已配置云开发环境ID为 `cloud1-0gjev5gfdef4d262`。如需修改，请在 `miniprogram/app.js` 文件中找到：
```javascript
wx.cloud.init({
  env: 'cloud1-0gjev5gfdef4d262', // 云开发环境 ID
  traceUser: true,
});
```

### 音频文件生成
项目已包含预生成的真实TTS音频文件（60个词汇，总计0.23MB），如需重新生成：

```bash
# 安装依赖（首次运行）
cd scripts && npm install

# 生成模拟音频文件（开发测试用）
cd scripts
node generate-audio-files.js

# 生成真实TTS音频文件（需要腾讯云API密钥）
export TENCENT_SECRET_ID="your-secret-id"
export TENCENT_SECRET_KEY="your-secret-key"
cd scripts
node generate-audio-files.js --real-tts

# 验证音频文件
cd scripts
node verify-audio-format.js
```

**当前状态**: ✅ 已优化为47个统一音频文件（85dB基准），100%完整率
详细说明请参考 `scripts/README.md`

## 🔊 音频系统优化说明

### 优化前（旧版本）
- 分散存储：`audio/55db/`、`audio/70db/`、`audio/85db/` 三个目录
- 复杂映射：需要维护分组和索引的复杂数据结构
- 文件冗余：同一词汇需要3个不同分贝的音频文件

### 优化后（当前版本）
- **统一存储**：所有音频文件存储在 `audio/` 根目录
- **85dB基准**：所有音频文件以85dB为标准录制
- **音量系数调节**：播放时通过音量系数实现不同分贝
  ```javascript
  55dB: 音量系数 0.3 (30%)
  70dB: 音量系数 0.6 (60%)
  85dB: 音量系数 1.0 (100%)
  ```
- **简化配置**：音频和图片通过文件名直接匹配
- **易于维护**：添加新词汇只需放入对应的音频和图片文件

### 音频文件验证
```bash
# 进入scripts目录
cd scripts

# 验证音频文件格式
npm run verify
```

### 本地开发
1. 打开小程序开发工具，导入本项目。
2. 上传并部署云函数（可选，已有降级方案）。
3. 确认音频文件已生成（`miniprogram/audio/` 目录）。
4. 点击开发工具中的预览按钮，查看效果。

## 目录结构
```
├── cloudfunctions/                    # 云函数目录
│   ├── generateTTS/                   # TTS生成云函数（降级备用）
│   ├── preGenerateAudio/              # 预生成音频云函数
│   ├── getAudioMapping/               # 获取音频映射云函数
│   ├── speechRecognition/             # 语音识别云函数
│   ├── saveTestResult/                # 保存测试结果云函数
│   ├── getTestHistory/                # 获取测试历史云函数
│   ├── generateReport/                # 生成报告云函数
│   └── getOpenId/                     # 获取OpenID云函数
├── miniprogram/                       # 小程序目录
│   ├── app.js
│   ├── app.json
│   ├── app.wxss
│   ├── audio/                         # 本地音频文件目录
│   │   ├── 55db/                      # 55分贝音频文件（20个）
│   │   ├── 70db/                      # 70分贝音频文件（20个）
│   │   └── 85db/                      # 85分贝音频文件（20个）
│   ├── config/                        # 配置文件目录
│   │   ├── audio-mapping.js           # 音频文件映射配置
│   │   └── audio-generation-report.json # 音频生成报告
│   ├── components/
│   │   └── cloudbase-badge/           # CloudBase徽章组件
│   ├── images/
│   │   └── powered-by-cloudbase-badge.svg
│   ├── pages/
│   │   ├── index/                     # 首页
│   │   ├── hearing-test/              # 听力测试页面
│   │   ├── test-result/               # 测试结果页面
│   │   └── test-history/              # 测试历史页面
│   └── sitemap.json
├── scripts/                           # 脚本目录
│   ├── generate-audio-files.js        # 音频文件生成脚本
│   └── README.md                      # 脚本使用说明
├── project.config.json
└── project.private.config.json
```

## 云开发使用示例

通过 `wx.cloud` 访问云开发服务：

```javascript
// 数据库操作
const db = wx.cloud.database();
const result = await db.collection('users').get(); // 查询数据
await db.collection('users').add({ data: { name: 'test' } }); // 添加数据

// 调用云函数
const funcResult = await wx.cloud.callFunction({ name: 'getOpenId' });

// 文件上传
const uploadResult = await wx.cloud.uploadFile({ cloudPath: 'test.jpg', filePath: tempFilePath });
// 调用数据模型
const models = wx.cloud.models;
```

## 扩展开发
您可以根据项目需求，添加新的云函数和页面，实现更多的云开发功能。

## ENV
TENCENT_SECRET_ID="AKIDpZ8YpzxvyueFeqr7C4jBRUp24tZ3NVUP"
TENCENT_SECRET_KEY="ALQK1jPwIVaUBj8NckjX8XpywiLo58IK"