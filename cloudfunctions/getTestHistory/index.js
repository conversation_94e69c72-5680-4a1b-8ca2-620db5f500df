const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { limit = 20, skip = 0, countOnly = false } = event

    console.log('获取测试历史:', {
      openid: wxContext.OPENID,
      limit,
      skip,
      countOnly
    })

    // 如果只需要统计数量
    if (countOnly) {
      const countResult = await db.collection('hearing_test_results')
        .where({
          openid: wxContext.OPENID
        })
        .count()

      console.log('统计测试记录数量:', countResult.total)

      return {
        success: true,
        totalCount: countResult.total,
        message: `您共有 ${countResult.total} 条听力测试记录`
      }
    }

    // 获取总数量（用于分页）
    const countResult = await db.collection('hearing_test_results')
      .where({
        openid: wxContext.OPENID
      })
      .count()

    // 查询用户的测试历史记录
    const result = await db.collection('hearing_test_results')
      .where({
        openid: wxContext.OPENID
      })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .skip(skip)
      .get()

    // 格式化返回数据
    const historyList = result.data.map(item => ({
      _id: item._id,
      testDate: item.testDate,
      // 听音辨图测试字段
      saiScore: item.saiScore,
      saiLevel: item.saiLevel,
      saiLevelClass: item.saiLevelClass,
      // 噪音测试字段
      overallAccuracy: item.overallAccuracy,
      totalCorrect: item.totalCorrect,
      totalTests: item.totalTests,
      // 通用字段
      testResults: item.testResults,
      testType: item.testType,
      createTime: item.createTime
    }))

    console.log('获取测试历史成功:', {
      currentPageCount: historyList.length,
      totalCount: countResult.total
    })

    return {
      success: true,
      data: historyList,
      currentPageCount: result.data.length,
      totalCount: countResult.total,
      message: '获取测试历史成功'
    }
    
  } catch (error) {
    console.error('获取测试历史失败:', error)
    return {
      success: false,
      error: error.message,
      data: [],
      message: '获取测试历史失败'
    }
  }
}
